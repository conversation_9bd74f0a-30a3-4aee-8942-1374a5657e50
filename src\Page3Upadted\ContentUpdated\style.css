/* Modern Data Table Component Styles */
.content-updated {
  background-color: #fbf9f4;
  display: flex;
  flex-direction: column;
  gap: clamp(16px, 3vw, 24px);
  padding: clamp(16px, 3vw, 24px);
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  box-sizing: border-box;
  min-height: 100vh;
}

/* Page Header */
.page-header {
  background: #ffffff;
  border-radius: 8px;
  padding: clamp(16px, 3vw, 24px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: clamp(16px, 3vw, 24px);
}

.page-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.page-title h1 {
  color: #0e5447;
  font-family: "Roboto-Medium", Helvetica, sans-serif;
  font-size: clamp(20px, 4vw, 28px);
  font-weight: 500;
  margin: 0;
  line-height: 1.2;
}

.info-icon {
  font-size: 18px;
  cursor: help;
  opacity: 0.7;
  transition: opacity 0.3s ease;
}

.info-icon:hover {
  opacity: 1;
}

/* Filters Section */
.filters-section {
  background: #ffffff;
  border-radius: 8px;
  padding: clamp(16px, 3vw, 24px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: clamp(16px, 3vw, 24px);
}

.filters-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: clamp(12px, 2vw, 20px);
  margin-bottom: 16px;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.filter-group label {
  color: #212121;
  font-family: "Roboto-Medium", Helvetica, sans-serif;
  font-size: 14px;
  font-weight: 500;
  margin: 0;
}

.filter-select {
  background-color: #ffffff;
  border: 1px solid #bdbdbd;
  border-radius: 4px;
  padding: 10px 12px;
  font-family: "Roboto", Helvetica, sans-serif;
  font-size: 14px;
  color: #616161;
  cursor: pointer;
  transition: all 0.3s ease;
  min-height: 40px;
}

.filter-select:hover {
  border-color: #ffc454;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.filter-select:focus {
  outline: none;
  border-color: #ffc454;
  box-shadow: 0 0 0 2px rgba(255, 196, 84, 0.3);
}

.search-row {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: end;
}

.search-group {
  flex: 1;
  min-width: 200px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.search-group label {
  color: #212121;
  font-family: "Roboto-Medium", Helvetica, sans-serif;
  font-size: 14px;
  font-weight: 500;
  margin: 0;
}

.search-input {
  background-color: #ffffff;
  border: 1px solid #bdbdbd;
  border-radius: 4px;
  padding: 10px 12px;
  font-family: "Roboto", Helvetica, sans-serif;
  font-size: 14px;
  color: #212121;
  transition: all 0.3s ease;
  min-height: 40px;
  box-sizing: border-box;
}

.search-input:hover {
  border-color: #ffc454;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.search-input:focus {
  outline: none;
  border-color: #ffc454;
  box-shadow: 0 0 0 2px rgba(255, 196, 84, 0.3);
}

.search-input::placeholder {
  color: #9e9e9e;
}

.clear-filters-btn {
  background-color: #f5f5f5;
  border: 1px solid #bdbdbd;
  border-radius: 4px;
  padding: 10px 16px;
  font-family: "Roboto-Medium", Helvetica, sans-serif;
  font-size: 14px;
  font-weight: 500;
  color: #616161;
  cursor: pointer;
  transition: all 0.3s ease;
  min-height: 40px;
  white-space: nowrap;
}

.clear-filters-btn:hover {
  background-color: #eeeeee;
  border-color: #9e9e9e;
  transform: translateY(-1px);
}

.clear-filters-btn:active {
  transform: translateY(0);
}

/* Table Container */
.table-container {
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin-bottom: clamp(16px, 3vw, 24px);
}

/* Table Actions */
.table-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e0e0e0;
  flex-wrap: wrap;
  gap: 16px;
}

.results-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
  color: #616161;
  font-family: "Roboto", Helvetica, sans-serif;
  font-size: 14px;
}

.action-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.action-btn {
  background-color: #ffffff;
  border: 1px solid #bdbdbd;
  border-radius: 4px;
  padding: 8px 16px;
  font-family: "Roboto-Medium", Helvetica, sans-serif;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  min-height: 36px;
}

.action-btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.action-btn:active:not(:disabled) {
  transform: translateY(0);
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.copy-btn {
  color: #0e5447;
  border-color: #0e5447;
}

.copy-btn:hover:not(:disabled) {
  background-color: #0e5447;
  color: #ffffff;
}

.delete-btn {
  color: #d32f2f;
  border-color: #d32f2f;
}

.delete-btn:hover:not(:disabled) {
  background-color: #d32f2f;
  color: #ffffff;
}

/* Data Table Wrapper */
.data-table-wrapper {
  overflow-x: auto;
  max-width: 100%;
}

/* Data Table */
.data-table {
  width: 100%;
  border-collapse: collapse;
  font-family: "Roboto", Helvetica, sans-serif;
  font-size: 14px;
  background-color: #ffffff;
}

.data-table th,
.data-table td {
  padding: 12px 16px;
  text-align: left;
  border-bottom: 1px solid #e0e0e0;
  vertical-align: middle;
}

.data-table th {
  background-color: #f8f9fa;
  color: #212121;
  font-weight: 600;
  font-size: 13px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  position: sticky;
  top: 0;
  z-index: 10;
}

.data-table th.sortable {
  cursor: pointer;
  user-select: none;
  transition: background-color 0.3s ease;
}

.data-table th.sortable:hover {
  background-color: #e8f5e8;
}

.data-table th.sortable.asc,
.data-table th.sortable.desc {
  background-color: #e8f5e8;
  color: #0e5447;
}

.data-table tbody tr {
  transition: background-color 0.3s ease;
}

.data-table tbody tr:hover {
  background-color: #f8f9fa;
}

.data-table tbody tr.even {
  background-color: #ffffff;
}

.data-table tbody tr.odd {
  background-color: #fafafa;
}

.data-table tbody tr.even:hover,
.data-table tbody tr.odd:hover {
  background-color: #f0f7ff;
}

/* Table Cell Styles */
.checkbox-column {
  width: 50px;
  text-align: center;
}

.table-checkbox {
  width: 16px;
  height: 16px;
  cursor: pointer;
  accent-color: #0e5447;
}

.reference-cell {
  font-family: "Roboto Mono", monospace;
  font-weight: 500;
  color: #0e5447;
  min-width: 120px;
}

.description-cell {
  min-width: 200px;
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.type-cell {
  min-width: 100px;
  font-weight: 500;
}

.status-cell {
  min-width: 120px;
}

.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-active {
  background-color: #e8f5e8;
  color: #2e7d32;
}

.status-pending {
  background-color: #fff3e0;
  color: #f57c00;
}

.status-completed {
  background-color: #e3f2fd;
  color: #1976d2;
}

.status-cancelled {
  background-color: #ffebee;
  color: #d32f2f;
}

.status-processing {
  background-color: #f3e5f5;
  color: #7b1fa2;
}

.amount-cell {
  font-family: "Roboto Mono", monospace;
  font-weight: 500;
  text-align: right;
  min-width: 100px;
  color: #0e5447;
}

.date-cell {
  min-width: 100px;
  color: #616161;
}

.category-cell {
  min-width: 100px;
  font-weight: 500;
}

/* Pagination Container */
.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-top: 1px solid #e0e0e0;
  background-color: #f8f9fa;
  flex-wrap: wrap;
  gap: 16px;
}

.pagination-info {
  display: flex;
  align-items: center;
  gap: 12px;
  color: #616161;
  font-family: "Roboto", Helvetica, sans-serif;
  font-size: 14px;
}

.pagination-info label {
  margin: 0;
  font-weight: 500;
}

.items-per-page-select {
  background-color: #ffffff;
  border: 1px solid #bdbdbd;
  border-radius: 4px;
  padding: 6px 8px;
  font-family: "Roboto", Helvetica, sans-serif;
  font-size: 14px;
  color: #616161;
  cursor: pointer;
  transition: all 0.3s ease;
}

.items-per-page-select:hover {
  border-color: #ffc454;
}

.items-per-page-select:focus {
  outline: none;
  border-color: #ffc454;
  box-shadow: 0 0 0 2px rgba(255, 196, 84, 0.3);
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.pagination-btn {
  background-color: #ffffff;
  border: 1px solid #bdbdbd;
  border-radius: 4px;
  padding: 8px 12px;
  font-family: "Roboto", Helvetica, sans-serif;
  font-size: 14px;
  color: #616161;
  cursor: pointer;
  transition: all 0.3s ease;
  min-height: 36px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.pagination-btn:hover:not(:disabled) {
  background-color: #f5f5f5;
  border-color: #9e9e9e;
  transform: translateY(-1px);
}

.pagination-btn:active:not(:disabled) {
  transform: translateY(0);
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-numbers {
  display: flex;
  gap: 4px;
  margin: 0 8px;
}

.page-number {
  background-color: #ffffff;
  border: 1px solid #bdbdbd;
  border-radius: 4px;
  padding: 8px 12px;
  font-family: "Roboto", Helvetica, sans-serif;
  font-size: 14px;
  color: #616161;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 36px;
  min-height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.page-number:hover {
  background-color: #f5f5f5;
  border-color: #9e9e9e;
  transform: translateY(-1px);
}

.page-number:active {
  transform: translateY(0);
}

.page-number.active {
  background-color: #0e5447;
  border-color: #0e5447;
  color: #ffffff;
}

.page-number.active:hover {
  background-color: #0a3d33;
  border-color: #0a3d33;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .content-updated {
    padding: 16px;
  }

  .filters-row {
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  }

  .data-table {
    font-size: 13px;
  }

  .data-table th,
  .data-table td {
    padding: 10px 12px;
  }
}

@media (max-width: 768px) {
  .content-updated {
    padding: 12px;
    gap: 16px;
  }

  .page-title h1 {
    font-size: 20px;
  }

  .filters-section {
    padding: 16px;
  }

  .filters-row {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .search-row {
    flex-direction: column;
    align-items: stretch;
  }

  .table-actions {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .results-info {
    text-align: center;
  }

  .action-buttons {
    justify-content: center;
  }

  .data-table-wrapper {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .data-table {
    font-size: 12px;
  }

  .data-table th,
  .data-table td {
    padding: 8px 10px;
  }

  .description-cell {
    max-width: 150px;
  }

  .pagination-container {
    flex-direction: column;
    gap: 12px;
  }

  .pagination-controls {
    justify-content: center;
  }

  .page-numbers {
    margin: 0;
  }
}

@media (max-width: 480px) {
  .content-updated {
    padding: 8px;
    gap: 12px;
  }

  .page-header,
  .filters-section,
  .table-container {
    padding: 12px;
  }

  .data-table th,
  .data-table td {
    padding: 6px 8px;
  }

  .description-cell {
    max-width: 100px;
  }

  .status-badge {
    font-size: 10px;
    padding: 2px 6px;
  }

  .pagination-btn,
  .page-number {
    padding: 6px 8px;
    font-size: 12px;
    min-height: 32px;
    min-width: 32px;
  }

  .action-btn {
    padding: 6px 12px;
    font-size: 12px;
    min-height: 32px;
  }
}

/* Print Styles */
@media print {
  .content-updated {
    background-color: white;
    box-shadow: none;
  }

  .filters-section,
  .table-actions,
  .pagination-container {
    display: none;
  }

  .data-table {
    font-size: 10px;
  }

  .data-table th,
  .data-table td {
    padding: 4px 6px;
  }
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .data-table th {
    background-color: #000000;
    color: #ffffff;
  }

  .data-table tbody tr:hover {
    background-color: #ffff00;
    color: #000000;
  }

  .status-badge {
    border: 2px solid currentColor;
  }
}

/* Focus Styles for Better Accessibility */
.filter-select:focus,
.search-input:focus,
.items-per-page-select:focus,
.table-checkbox:focus,
.action-btn:focus,
.pagination-btn:focus,
.page-number:focus,
.clear-filters-btn:focus {
  outline: 2px solid #ffc454;
  outline-offset: 2px;
}

/* Loading State (for future enhancement) */
.table-loading {
  opacity: 0.6;
  pointer-events: none;
  position: relative;
}

.table-loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}