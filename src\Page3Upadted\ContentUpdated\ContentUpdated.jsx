import React, { useState, useMemo } from "react";
import "./style.css";

// Sample data for the table
const generateSampleData = () => {
  const data = [];
  const statuses = ['Active', 'Pending', 'Completed', 'Cancelled', 'Processing'];
  const types = ['Type A', 'Type B', 'Type C', 'Type D'];

  for (let i = 1; i <= 150; i++) {
    data.push({
      id: i,
      reference: `REF${String(i).padStart(6, '0')}`,
      description: `Item Description ${i}`,
      type: types[Math.floor(Math.random() * types.length)],
      status: statuses[Math.floor(Math.random() * statuses.length)],
      amount: (Math.random() * 10000).toFixed(2),
      date: new Date(2024, Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1).toLocaleDateString(),
      category: `Category ${String.fromCharCode(65 + Math.floor(Math.random() * 5))}`
    });
  }
  return data;
};

export default function ContentUpdated() {
  const [data] = useState(generateSampleData());
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortConfig, setSortConfig] = useState({ key: null, direction: 'asc' });
  const [selectedItems, setSelectedItems] = useState(new Set());
  const [filters, setFilters] = useState({
    type: '',
    status: '',
    category: ''
  });

  // Filter and search data
  const filteredData = useMemo(() => {
    return data.filter(item => {
      const matchesSearch = Object.values(item).some(value =>
        value.toString().toLowerCase().includes(searchTerm.toLowerCase())
      );
      const matchesFilters = Object.entries(filters).every(([key, value]) =>
        !value || item[key] === value
      );
      return matchesSearch && matchesFilters;
    });
  }, [data, searchTerm, filters]);

  // Sort data
  const sortedData = useMemo(() => {
    if (!sortConfig.key) return filteredData;

    return [...filteredData].sort((a, b) => {
      if (a[sortConfig.key] < b[sortConfig.key]) {
        return sortConfig.direction === 'asc' ? -1 : 1;
      }
      if (a[sortConfig.key] > b[sortConfig.key]) {
        return sortConfig.direction === 'asc' ? 1 : -1;
      }
      return 0;
    });
  }, [filteredData, sortConfig]);

  // Paginate data
  const paginatedData = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    return sortedData.slice(startIndex, startIndex + itemsPerPage);
  }, [sortedData, currentPage, itemsPerPage]);

  const totalPages = Math.ceil(sortedData.length / itemsPerPage);

  // Handle sorting
  const handleSort = (key) => {
    setSortConfig(prevConfig => ({
      key,
      direction: prevConfig.key === key && prevConfig.direction === 'asc' ? 'desc' : 'asc'
    }));
  };

  // Handle selection
  const handleSelectAll = (checked) => {
    if (checked) {
      setSelectedItems(new Set(paginatedData.map(item => item.id)));
    } else {
      setSelectedItems(new Set());
    }
  };

  const handleSelectItem = (id, checked) => {
    const newSelected = new Set(selectedItems);
    if (checked) {
      newSelected.add(id);
    } else {
      newSelected.delete(id);
    }
    setSelectedItems(newSelected);
  };

  // Get unique values for filters
  const getUniqueValues = (key) => {
    return [...new Set(data.map(item => item[key]))].sort();
  };

  return (
    <div className="content-updated">
      {/* Page Header */}
      <div className="page-header">
        <div className="page-title">
          <h1>Data Management Dashboard</h1>
          <div className="info-icon" title="Data management and analysis tools">
            ℹ️
          </div>
        </div>
      </div>

      {/* Filters and Search Section */}
      <div className="filters-section">
        <div className="filters-row">
          <div className="filter-group">
            <label htmlFor="type-filter">Type</label>
            <select
              id="type-filter"
              value={filters.type}
              onChange={(e) => setFilters(prev => ({ ...prev, type: e.target.value }))}
              className="filter-select"
            >
              <option value="">All Types</option>
              {getUniqueValues('type').map(type => (
                <option key={type} value={type}>{type}</option>
              ))}
            </select>
          </div>

          <div className="filter-group">
            <label htmlFor="status-filter">Status</label>
            <select
              id="status-filter"
              value={filters.status}
              onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
              className="filter-select"
            >
              <option value="">All Statuses</option>
              {getUniqueValues('status').map(status => (
                <option key={status} value={status}>{status}</option>
              ))}
            </select>
          </div>

          <div className="filter-group">
            <label htmlFor="category-filter">Category</label>
            <select
              id="category-filter"
              value={filters.category}
              onChange={(e) => setFilters(prev => ({ ...prev, category: e.target.value }))}
              className="filter-select"
            >
              <option value="">All Categories</option>
              {getUniqueValues('category').map(category => (
                <option key={category} value={category}>{category}</option>
              ))}
            </select>
          </div>
        </div>

        <div className="search-row">
          <div className="search-group">
            <label htmlFor="search-input">Search</label>
            <input
              id="search-input"
              type="text"
              placeholder="Search all fields..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="search-input"
            />
          </div>

          <button
            className="clear-filters-btn"
            onClick={() => {
              setSearchTerm('');
              setFilters({ type: '', status: '', category: '' });
            }}
          >
            Clear Filters
          </button>
        </div>
      </div>

      {/* Data Table Section */}
      <div className="table-container">
        {/* Table Actions */}
        <div className="table-actions">
          <div className="results-info">
            <span>Total Items Found: {sortedData.length}</span>
            <span>Showing {((currentPage - 1) * itemsPerPage) + 1}-{Math.min(currentPage * itemsPerPage, sortedData.length)} of {sortedData.length}</span>
          </div>

          <div className="action-buttons">
            <button
              className="action-btn copy-btn"
              disabled={selectedItems.size === 0}
              onClick={() => {
                const selectedData = paginatedData.filter(item => selectedItems.has(item.id));
                console.log('Copy selected items:', selectedData);
              }}
            >
              📋 Copy ({selectedItems.size})
            </button>

            <button
              className="action-btn delete-btn"
              disabled={selectedItems.size === 0}
              onClick={() => {
                if (window.confirm(`Delete ${selectedItems.size} selected items?`)) {
                  console.log('Delete selected items:', Array.from(selectedItems));
                  setSelectedItems(new Set());
                }
              }}
            >
              🗑️ Delete ({selectedItems.size})
            </button>
          </div>
        </div>

        {/* Data Table */}
        <div className="data-table-wrapper">
          <table className="data-table">
            <thead>
              <tr>
                <th className="checkbox-column">
                  <input
                    type="checkbox"
                    checked={selectedItems.size === paginatedData.length && paginatedData.length > 0}
                    onChange={(e) => handleSelectAll(e.target.checked)}
                    className="table-checkbox"
                  />
                </th>
                <th
                  className={`sortable ${sortConfig.key === 'reference' ? sortConfig.direction : ''}`}
                  onClick={() => handleSort('reference')}
                >
                  Reference {sortConfig.key === 'reference' && (sortConfig.direction === 'asc' ? '↑' : '↓')}
                </th>
                <th
                  className={`sortable ${sortConfig.key === 'description' ? sortConfig.direction : ''}`}
                  onClick={() => handleSort('description')}
                >
                  Description {sortConfig.key === 'description' && (sortConfig.direction === 'asc' ? '↑' : '↓')}
                </th>
                <th
                  className={`sortable ${sortConfig.key === 'type' ? sortConfig.direction : ''}`}
                  onClick={() => handleSort('type')}
                >
                  Type {sortConfig.key === 'type' && (sortConfig.direction === 'asc' ? '↑' : '↓')}
                </th>
                <th
                  className={`sortable ${sortConfig.key === 'status' ? sortConfig.direction : ''}`}
                  onClick={() => handleSort('status')}
                >
                  Status {sortConfig.key === 'status' && (sortConfig.direction === 'asc' ? '↑' : '↓')}
                </th>
                <th
                  className={`sortable ${sortConfig.key === 'amount' ? sortConfig.direction : ''}`}
                  onClick={() => handleSort('amount')}
                >
                  Amount {sortConfig.key === 'amount' && (sortConfig.direction === 'asc' ? '↑' : '↓')}
                </th>
                <th
                  className={`sortable ${sortConfig.key === 'date' ? sortConfig.direction : ''}`}
                  onClick={() => handleSort('date')}
                >
                  Date {sortConfig.key === 'date' && (sortConfig.direction === 'asc' ? '↑' : '↓')}
                </th>
                <th
                  className={`sortable ${sortConfig.key === 'category' ? sortConfig.direction : ''}`}
                  onClick={() => handleSort('category')}
                >
                  Category {sortConfig.key === 'category' && (sortConfig.direction === 'asc' ? '↑' : '↓')}
                </th>
              </tr>
            </thead>
            <tbody>
              {paginatedData.map((item, index) => (
                <tr key={item.id} className={index % 2 === 0 ? 'even' : 'odd'}>
                  <td className="checkbox-column">
                    <input
                      type="checkbox"
                      checked={selectedItems.has(item.id)}
                      onChange={(e) => handleSelectItem(item.id, e.target.checked)}
                      className="table-checkbox"
                    />
                  </td>
                  <td className="reference-cell">{item.reference}</td>
                  <td className="description-cell">{item.description}</td>
                  <td className="type-cell">{item.type}</td>
                  <td className="status-cell">
                    <span className={`status-badge status-${item.status.toLowerCase()}`}>
                      {item.status}
                    </span>
                  </td>
                  <td className="amount-cell">${item.amount}</td>
                  <td className="date-cell">{item.date}</td>
                  <td className="category-cell">{item.category}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        <div className="pagination-container">
          <div className="pagination-info">
            <label htmlFor="items-per-page">Items per page:</label>
            <select
              id="items-per-page"
              value={itemsPerPage}
              onChange={(e) => {
                setItemsPerPage(Number(e.target.value));
                setCurrentPage(1);
              }}
              className="items-per-page-select"
            >
              <option value={5}>5</option>
              <option value={10}>10</option>
              <option value={25}>25</option>
              <option value={50}>50</option>
              <option value={100}>100</option>
            </select>
          </div>

          <div className="pagination-controls">
            <button
              className="pagination-btn"
              onClick={() => setCurrentPage(1)}
              disabled={currentPage === 1}
            >
              ⏮️ First
            </button>

            <button
              className="pagination-btn"
              onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
              disabled={currentPage === 1}
            >
              ⬅️ Previous
            </button>

            <div className="page-numbers">
              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                let pageNum;
                if (totalPages <= 5) {
                  pageNum = i + 1;
                } else if (currentPage <= 3) {
                  pageNum = i + 1;
                } else if (currentPage >= totalPages - 2) {
                  pageNum = totalPages - 4 + i;
                } else {
                  pageNum = currentPage - 2 + i;
                }

                return (
                  <button
                    key={pageNum}
                    className={`page-number ${currentPage === pageNum ? 'active' : ''}`}
                    onClick={() => setCurrentPage(pageNum)}
                  >
                    {pageNum}
                  </button>
                );
              })}
            </div>

            <button
              className="pagination-btn"
              onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
              disabled={currentPage === totalPages}
            >
              Next ➡️
            </button>

            <button
              className="pagination-btn"
              onClick={() => setCurrentPage(totalPages)}
              disabled={currentPage === totalPages}
            >
              Last ⏭️
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
